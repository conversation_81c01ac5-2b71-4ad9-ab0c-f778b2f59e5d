import { SubscriptionTierConfig } from '@/types/subscription.types';
import { SubscriptionTier } from '@/types/schema.types';

/**
 * Centralized subscription tier configuration
 * This is the single source of truth for all subscription-related settings
 */
export const SUBSCRIPTION_TIERS: Record<SubscriptionTier, SubscriptionTierConfig> = {
  free: {
    tier: 'free',
    name: 'free',
    displayName: 'Free',
    price: {
      monthly: 0,
    },
    description: 'Perfect for getting started with solo RPG adventures',
    limits: {
      max_worlds: 3,
      max_characters_per_world: 5,
      ai_messages_per_day: 50,
    },
    features: {
      custom_world_settings: false,
      priority_support: false,
      advanced_ai_models: false,
      export_data: false,
      early_access_features: false,
    },
    badge: {
      icon: '🆓',
      color: 'gray',
    },
    order: 1,
  },
  
  pro: {
    tier: 'pro',
    name: 'pro',
    displayName: 'Pro',
    price: {
      monthly: 999, // $9.99
      yearly: 9999, // $99.99 (2 months free)
    },
    description: 'Enhanced features for dedicated solo RPG enthusiasts',
    limits: {
      max_worlds: 10,
      max_characters_per_world: 15,
      ai_messages_per_day: 200,
    },
    features: {
      custom_world_settings: true,
      priority_support: true,
      advanced_ai_models: false,
      export_data: true,
      early_access_features: false,
    },
    badge: {
      icon: '⭐',
      color: 'primary',
    },
    popular: true,
    order: 2,
  },
  
  pro_plus: {
    tier: 'pro_plus',
    name: 'pro_plus',
    displayName: 'Pro+',
    price: {
      monthly: 1999, // $19.99
      yearly: 19999, // $199.99 (2 months free)
    },
    description: 'Ultimate experience with unlimited worlds and advanced AI',
    limits: {
      max_worlds: -1, // unlimited
      max_characters_per_world: -1, // unlimited
      ai_messages_per_day: 1000,
    },
    features: {
      custom_world_settings: true,
      priority_support: true,
      advanced_ai_models: true,
      export_data: true,
      early_access_features: true,
    },
    badge: {
      icon: '👑',
      color: 'accent',
      gradient: true,
    },
    order: 3,
  },
};

/**
 * Get all subscription tiers in display order
 */
export function getAllTiers(): SubscriptionTierConfig[] {
  return Object.values(SUBSCRIPTION_TIERS).sort((a, b) => a.order - b.order);
}

/**
 * Get configuration for a specific tier
 */
export function getTierConfig(tier: SubscriptionTier): SubscriptionTierConfig {
  const config = SUBSCRIPTION_TIERS[tier];
  if (!config) {
    throw new Error(`Invalid subscription tier: ${tier}`);
  }
  return config;
}

/**
 * Check if a tier has unlimited access for a specific limit type
 */
export function isUnlimited(tier: SubscriptionTier, limitType: 'worlds' | 'characters'): boolean {
  const config = getTierConfig(tier);
  switch (limitType) {
    case 'worlds':
      return config.limits.max_worlds === -1;
    case 'characters':
      return config.limits.max_characters_per_world === -1;
    default:
      return false;
  }
}

/**
 * Get the limit value for a specific tier and limit type
 */
export function getLimit(tier: SubscriptionTier, limitType: 'worlds' | 'characters' | 'ai_messages'): number {
  const config = getTierConfig(tier);
  switch (limitType) {
    case 'worlds':
      return config.limits.max_worlds;
    case 'characters':
      return config.limits.max_characters_per_world;
    case 'ai_messages':
      return config.limits.ai_messages_per_day;
    default:
      throw new Error(`Invalid limit type: ${limitType}`);
  }
}

/**
 * Format price for display
 */
export function formatPrice(priceInCents: number): string {
  if (priceInCents === 0) return 'Free';
  return `$${(priceInCents / 100).toFixed(2)}`;
}

/**
 * Get monthly price for a tier
 */
export function getMonthlyPrice(tier: SubscriptionTier): string {
  const config = getTierConfig(tier);
  return formatPrice(config.price.monthly);
}

/**
 * Get yearly price for a tier (if available)
 */
export function getYearlyPrice(tier: SubscriptionTier): string | null {
  const config = getTierConfig(tier);
  return config.price.yearly ? formatPrice(config.price.yearly) : null;
}

/**
 * Calculate yearly savings
 */
export function getYearlySavings(tier: SubscriptionTier): number {
  const config = getTierConfig(tier);
  if (!config.price.yearly) return 0;
  
  const monthlyTotal = config.price.monthly * 12;
  return monthlyTotal - config.price.yearly;
}

/**
 * Validate subscription configuration
 */
export function validateConfig(): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // Check that all required tiers exist
  const requiredTiers: SubscriptionTier[] = ['free', 'pro', 'pro_plus'];
  for (const tier of requiredTiers) {
    if (!SUBSCRIPTION_TIERS[tier]) {
      errors.push(`Missing configuration for tier: ${tier}`);
    }
  }
  
  // Check that limits make sense (higher tiers should have higher or equal limits)
  const tiers = getAllTiers();
  for (let i = 1; i < tiers.length; i++) {
    const current = tiers[i];
    const previous = tiers[i - 1];
    
    // Skip unlimited checks
    if (current.limits.max_worlds !== -1 && previous.limits.max_worlds !== -1) {
      if (current.limits.max_worlds < previous.limits.max_worlds) {
        errors.push(`${current.tier} has fewer max_worlds than ${previous.tier}`);
      }
    }
    
    if (current.limits.max_characters_per_world !== -1 && previous.limits.max_characters_per_world !== -1) {
      if (current.limits.max_characters_per_world < previous.limits.max_characters_per_world) {
        errors.push(`${current.tier} has fewer max_characters_per_world than ${previous.tier}`);
      }
    }
    
    if (current.limits.ai_messages_per_day < previous.limits.ai_messages_per_day) {
      errors.push(`${current.tier} has fewer ai_messages_per_day than ${previous.tier}`);
    }
  }
  
  return {
    valid: errors.length === 0,
    errors,
  };
}
