import NextAuth from "next-auth"

declare module "next-auth" {
  /**
   * Extends the built-in User type with additional properties
   */
  interface User {
    id: string
  }

  /**
   * Extends the built-in Session type with additional properties
   */
  interface Session {
    user: {
      id: string
      name?: string | null
      email?: string | null
      image?: string | null
    }
    /** Supabase JWT token for client-side authenticated requests */
    supabaseAccessToken?: string
    accessToken?: string
  }
} 