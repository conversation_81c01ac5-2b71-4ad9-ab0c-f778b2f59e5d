# SoloQuest Enhanced Subscription System

## Overview

This enhanced subscription system provides centralized configuration management and comprehensive usage tracking with limit enforcement for the SoloQuest application.

## Features Implemented

### 1. Centralized Subscription Configuration
- **File**: `src/config/subscription.config.ts`
- **Types**: `src/types/subscription.types.ts`
- Single source of truth for all subscription tiers and features
- Easy modification without code changes across multiple files
- TypeScript type safety throughout the system

### 2. Usage Tracking Database Schema
- **Migration**: `supabase/migrations/add_usage_tracking.sql`
- **Tables**: `user_usage`, `world_character_counts`
- **Functions**: Automatic increment/decrement with daily reset for AI messages
- Row Level Security (RLS) enabled for data protection

### 3. Usage Tracking Service Layer
- **File**: `src/lib/usage-tracking.service.ts`
- Functions for checking limits, incrementing usage, and retrieving stats
- Handles unlimited tiers and daily resets automatically

### 4. Limit Enforcement Middleware
- **File**: `src/lib/subscription-middleware.ts`
- API wrapper for automatic limit checking and enforcement
- Helper functions for UI display and error handling

### 5. Usage Display UI Components
- **File**: `src/components/UsageDisplay.tsx`
- Progress bars, usage cards, limit warnings, and dashboard
- Responsive design with visual indicators for approaching limits

## Database Schema

### user_usage Table
```sql
CREATE TABLE user_usage (
    id uuid PRIMARY KEY,
    user_id uuid REFERENCES users(id),
    worlds_created integer DEFAULT 0,
    characters_created integer DEFAULT 0,
    ai_messages_today integer DEFAULT 0,
    ai_messages_reset_date date DEFAULT CURRENT_DATE,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);
```

### world_character_counts Table
```sql
CREATE TABLE world_character_counts (
    id uuid PRIMARY KEY,
    user_id uuid REFERENCES users(id),
    world_id uuid REFERENCES worlds(id),
    character_count integer DEFAULT 0,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);
```

## Subscription Tiers Configuration

### Free Tier
- 3 worlds maximum
- 5 characters per world
- 50 AI messages per day
- Basic features only

### Pro Tier ($9.99/month)
- 10 worlds maximum
- 15 characters per world
- 200 AI messages per day
- Custom world settings, priority support, data export

### Pro+ Tier ($19.99/month)
- Unlimited worlds and characters
- 1000 AI messages per day
- Advanced AI models, early access features

## API Endpoints

### GET /api/v1/users/[userId]/subscription
Returns subscription tier, configuration, and usage statistics.

### PATCH /api/v1/users/[userId]/subscription
Updates subscription tier (admin only).

### GET /api/v1/users/[userId]/usage
Returns detailed usage statistics with optional world-specific data.

## Integration Examples

### World Creation with Limit Enforcement
```typescript
import { withSubscriptionLimitAPI } from '@/lib/subscription-middleware';

export const POST = withSubscriptionLimitAPI(
  async (request: NextRequest) => {
    // Your world creation logic
    return NextResponse.json({ success: true });
  },
  async (request: NextRequest) => ({
    userId: session.user.id,
    limitType: 'worlds'
  })
);
```

### Character Creation with World-Specific Limits
```typescript
export const POST = withSubscriptionLimitAPI(
  async (request: NextRequest) => {
    // Your character creation logic
    return NextResponse.json({ success: true });
  },
  async (request: NextRequest) => {
    const body = await request.json();
    return {
      userId: session.user.id,
      limitType: 'characters',
      worldId: body.world_id
    };
  }
);
```

### Manual Usage Tracking
```typescript
import { checkUsageLimit, incrementUsage } from '@/lib/usage-tracking.service';

// Check limit before action
const limitCheck = await checkUsageLimit({
  userId: session.user.id,
  limitType: 'ai_messages'
});

if (!limitCheck.allowed) {
  return NextResponse.json({ error: 'Limit exceeded' }, { status: 429 });
}

// Perform action...

// Increment usage after success
await incrementUsage(session.user.id, 'ai_messages');
```

## UI Components Usage

### Usage Dashboard
```tsx
import { UsageDashboard } from '@/components/UsageDisplay';

<UsageDashboard
  worldsUsage={usageStats.worlds}
  aiMessagesUsage={usageStats.ai_messages_daily}
  subscriptionTier={user.subscription_tier}
/>
```

### Individual Usage Cards
```tsx
import { UsageStatsCard } from '@/components/UsageDisplay';

<UsageStatsCard
  title="Worlds Created"
  usage={worldsUsage}
  limitType="worlds"
  icon="🌍"
/>
```

### Limit Warnings
```tsx
import { LimitWarning } from '@/components/UsageDisplay';

{isAtLimit && (
  <LimitWarning
    limitType="worlds"
    current={usage.current}
    limit={usage.limit}
    tier={user.subscription_tier}
    onUpgrade={() => router.push('/upgrade')}
  />
)}
```

## Testing

### Test Pages
- `/test-usage-system` - Comprehensive system testing
- `/test-subscription-api` - API endpoint testing

### Manual Testing Steps
1. Run database migration: `supabase/migrations/add_usage_tracking.sql`
2. Visit test pages to verify functionality
3. Test limit enforcement by reaching tier limits
4. Verify usage increments and decrements correctly
5. Test daily reset for AI messages

## Error Handling

### SubscriptionLimitError
```typescript
try {
  await enforceUsageLimit({ userId, limitType: 'worlds' });
} catch (error) {
  if (error instanceof SubscriptionLimitError) {
    return NextResponse.json({
      error: error.message,
      code: 'SUBSCRIPTION_LIMIT_EXCEEDED',
      details: {
        limitType: error.limitType,
        current: error.current,
        limit: error.limit,
        tier: error.tier
      }
    }, { status: 429 });
  }
}
```

## Configuration Management

### Updating Subscription Tiers
Edit `src/config/subscription.config.ts`:
```typescript
export const SUBSCRIPTION_TIERS = {
  free: {
    limits: {
      max_worlds: 5, // Changed from 3
      // ... other limits
    }
  }
};
```

### Adding New Features
1. Add feature to `SubscriptionFeatures` type
2. Update tier configurations
3. Add UI display logic
4. Update comparison components

## Deployment Checklist

- [ ] Run database migration
- [ ] Update environment variables if needed
- [ ] Test all API endpoints
- [ ] Verify UI components display correctly
- [ ] Test limit enforcement
- [ ] Verify usage tracking accuracy
- [ ] Test subscription tier changes
- [ ] Validate error handling

## Future Enhancements

1. **Payment Integration**: Connect with Stripe/PayPal for automatic tier management
2. **Usage Analytics**: Add detailed usage reporting and analytics
3. **Flexible Limits**: Allow custom limits per user
4. **Usage Alerts**: Email notifications for approaching limits
5. **Admin Dashboard**: Interface for managing user subscriptions
6. **Usage History**: Track usage over time for insights

## Support

For questions or issues with the subscription system:
1. Check the test pages for system status
2. Review error logs for SubscriptionLimitError instances
3. Verify database migration was applied correctly
4. Ensure all required environment variables are set
