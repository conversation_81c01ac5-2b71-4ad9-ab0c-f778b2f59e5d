import { SubscriptionTier } from '@/types/schema.types';

interface SubscriptionBadgeProps {
  tier: SubscriptionTier;
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
  className?: string;
}

export default function SubscriptionBadge({
  tier,
  size = 'md',
  showIcon = true,
  className = ''
}: SubscriptionBadgeProps) {
  const getBadgeConfig = (tier: SubscriptionTier) => {
    switch (tier) {
      case 'free':
        return {
          label: 'Free',
          bgColor: 'bg-gray-500/20',
          textColor: 'text-gray-300',
          borderColor: 'border-gray-500/30',
          icon: ''
        };
      case 'pro':
        return {
          label: 'Pro',
          bgColor: 'bg-primary/20',
          textColor: 'text-primary',
          borderColor: 'border-primary/30',
          icon: '⭐'
        };
      case 'pro_plus':
        return {
          label: 'Pro+',
          bgColor: 'bg-gradient-to-r from-accent/20 to-primary/20',
          textColor: 'text-accent',
          borderColor: 'border-accent/30',
          icon: '👑'
        };
      default:
        return {
          label: 'Free',
          bgColor: 'bg-gray-500/20',
          textColor: 'text-gray-300',
          borderColor: 'border-gray-500/30',
          icon: ''
        };
    }
  };

  const getSizeClasses = (size: 'sm' | 'md' | 'lg') => {
    switch (size) {
      case 'sm':
        return 'px-2 py-1 text-xs';
      case 'md':
        return 'px-3 py-1 text-sm';
      case 'lg':
        return 'px-4 py-2 text-base';
      default:
        return 'px-3 py-1 text-sm';
    }
  };

  const config = getBadgeConfig(tier);
  const sizeClasses = getSizeClasses(size);

  return (
    <span
      className={`
        inline-flex items-center gap-1 rounded-full border font-medium
        ${config.bgColor} ${config.textColor} ${config.borderColor}
        ${sizeClasses}
        ${className}
      `}
    >
      {showIcon && <span className="text-xs">{config.icon}</span>}
      {config.label}
    </span>
  );
}

// Helper component for subscription tier comparison
interface SubscriptionComparisonProps {
  currentTier: SubscriptionTier;
  className?: string;
}

export function SubscriptionComparison({ currentTier, className = '' }: SubscriptionComparisonProps) {
  const tiers: { tier: SubscriptionTier; name: string; price: string; features: string[] }[] = [
    {
      tier: 'free',
      name: 'Free',
      price: '$0/month',
      features: [
        '3 worlds maximum',
        '5 characters per world',
        '50 AI messages per day',
        'Basic world settings'
      ]
    },
    {
      tier: 'pro',
      name: 'Pro',
      price: '$9.99/month',
      features: [
        '10 worlds maximum',
        '15 characters per world',
        '200 AI messages per day',
        'Custom world settings',
        'Priority support',
        'Data export'
      ]
    },
    {
      tier: 'pro_plus',
      name: 'Pro+',
      price: '$19.99/month',
      features: [
        'Unlimited worlds',
        'Unlimited characters',
        '1000 AI messages per day',
        'Advanced AI models',
        'Priority support',
        'Data export',
        'Early access features'
      ]
    }
  ];

  return (
    <div className={`grid grid-cols-1 md:grid-cols-3 gap-4 ${className}`}>
      {tiers.map((tierInfo) => (
        <div
          key={tierInfo.tier}
          className={`
            p-4 rounded-lg border transition-all
            ${currentTier === tierInfo.tier
              ? 'border-primary bg-primary/10'
              : 'border-gray-800 bg-background-darker'
            }
          `}
        >
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-medieval text-lg">{tierInfo.name}</h3>
            <SubscriptionBadge tier={tierInfo.tier} size="sm" />
          </div>
          <div className="text-2xl font-bold text-primary mb-4">{tierInfo.price}</div>
          <ul className="space-y-2">
            {tierInfo.features.map((feature, index) => (
              <li key={index} className="flex items-center gap-2 text-sm text-text-secondary">
                <span className="text-primary">✓</span>
                {feature}
              </li>
            ))}
          </ul>
          {currentTier === tierInfo.tier && (
            <div className="mt-4 text-center">
              <span className="text-primary text-sm font-medium">Current Plan</span>
            </div>
          )}
        </div>
      ))}
    </div>
  );
}
