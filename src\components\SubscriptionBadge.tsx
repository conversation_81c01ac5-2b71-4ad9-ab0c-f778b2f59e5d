import { SubscriptionTier } from '@/types/schema.types';
import { getTierConfig, getAllTiers, getMonthlyPrice } from '@/config/subscription.config';

interface SubscriptionBadgeProps {
  tier: SubscriptionTier;
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
  className?: string;
}

export default function SubscriptionBadge({
  tier,
  size = 'md',
  showIcon = true,
  className = ''
}: SubscriptionBadgeProps) {
  const getBadgeConfig = (tier: SubscriptionTier) => {
    const config = getTierConfig(tier);

    const colorMap = {
      gray: {
        bgColor: 'bg-gray-500/20',
        textColor: 'text-gray-300',
        borderColor: 'border-gray-500/30',
      },
      primary: {
        bgColor: 'bg-primary/20',
        textColor: 'text-primary',
        borderColor: 'border-primary/30',
      },
      accent: {
        bgColor: config.badge.gradient
          ? 'bg-gradient-to-r from-accent/20 to-primary/20'
          : 'bg-accent/20',
        textColor: 'text-accent',
        borderColor: 'border-accent/30',
      },
    };

    const colors = colorMap[config.badge.color];

    return {
      label: config.displayName,
      bgColor: colors.bgColor,
      textColor: colors.textColor,
      borderColor: colors.borderColor,
      icon: config.badge.icon
    };
  };

  const getSizeClasses = (size: 'sm' | 'md' | 'lg') => {
    switch (size) {
      case 'sm':
        return 'px-2 py-1 text-xs';
      case 'md':
        return 'px-3 py-1 text-sm';
      case 'lg':
        return 'px-4 py-2 text-base';
      default:
        return 'px-3 py-1 text-sm';
    }
  };

  const config = getBadgeConfig(tier);
  const sizeClasses = getSizeClasses(size);

  return (
    <span
      className={`
        inline-flex items-center gap-1 rounded-full border font-medium
        ${config.bgColor} ${config.textColor} ${config.borderColor}
        ${sizeClasses}
        ${className}
      `}
    >
      {showIcon && <span className="text-xs">{config.icon}</span>}
      {config.label}
    </span>
  );
}

// Helper component for subscription tier comparison
interface SubscriptionComparisonProps {
  currentTier: SubscriptionTier;
  className?: string;
}

export function SubscriptionComparison({ currentTier, className = '' }: SubscriptionComparisonProps) {
  const tiers = getAllTiers().map(config => {
    const features: string[] = [];

    // Add limit-based features
    if (config.limits.max_worlds === -1) {
      features.push('Unlimited worlds');
    } else {
      features.push(`${config.limits.max_worlds} worlds maximum`);
    }

    if (config.limits.max_characters_per_world === -1) {
      features.push('Unlimited characters');
    } else {
      features.push(`${config.limits.max_characters_per_world} characters per world`);
    }

    features.push(`${config.limits.ai_messages_per_day} AI messages per day`);

    // Add boolean features
    if (config.features.custom_world_settings) features.push('Custom world settings');
    else features.push('Basic world settings');

    if (config.features.priority_support) features.push('Priority support');
    if (config.features.advanced_ai_models) features.push('Advanced AI models');
    if (config.features.export_data) features.push('Data export');
    if (config.features.early_access_features) features.push('Early access features');

    return {
      tier: config.tier,
      name: config.displayName,
      price: getMonthlyPrice(config.tier) + '/month',
      features
    };
  });

  return (
    <div className={`grid grid-cols-1 md:grid-cols-3 gap-4 ${className}`}>
      {tiers.map((tierInfo) => (
        <div
          key={tierInfo.tier}
          className={`
            p-4 rounded-lg border transition-all
            ${currentTier === tierInfo.tier
              ? 'border-primary bg-primary/10'
              : 'border-gray-800 bg-background-darker'
            }
          `}
        >
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-medieval text-lg">{tierInfo.name}</h3>
            <SubscriptionBadge tier={tierInfo.tier} size="sm" />
          </div>
          <div className="text-2xl font-bold text-primary mb-4">{tierInfo.price}</div>
          <ul className="space-y-2">
            {tierInfo.features.map((feature, index) => (
              <li key={index} className="flex items-center gap-2 text-sm text-text-secondary">
                <span className="text-primary">✓</span>
                {feature}
              </li>
            ))}
          </ul>
          {currentTier === tierInfo.tier && (
            <div className="mt-4 text-center">
              <span className="text-primary text-sm font-medium">Current Plan</span>
            </div>
          )}
        </div>
      ))}
    </div>
  );
}
