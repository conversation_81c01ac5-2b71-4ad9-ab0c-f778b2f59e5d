import { supabaseAdmin } from './supabase-admin';
import { getTierConfig, getLimit, isUnlimited } from '@/config/subscription.config';
import {
  UserUsage,
  SubscriptionTier
} from '@/types/schema.types';
import {
  UsageCheckResult,
  UserUsageStats,
  LimitType,
  LimitCheckOptions,
  SubscriptionLimitError
} from '@/types/subscription.types';

/**
 * Initialize user usage tracking record
 */
export async function initializeUserUsage(userId: string): Promise<void> {
  try {
    const { error } = await supabaseAdmin.rpc('initialize_user_usage', {
      p_user_id: userId
    });

    if (error) {
      console.error('Error initializing user usage:', error);
      throw new Error(`Failed to initialize user usage: ${error.message}`);
    }
  } catch (error) {
    console.error('Error in initializeUserUsage:', error);
    throw error;
  }
}

/**
 * Get current user usage statistics
 */
export async function getUserUsage(userId: string): Promise<UserUsage | null> {
  try {
    const { data, error } = await supabaseAdmin
      .from('user_usage')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error('Error getting user usage:', error);
      throw new Error(`Failed to get user usage: ${error.message}`);
    }

    return data || null;
  } catch (error) {
    console.error('Error in getUserUsage:', error);
    throw error;
  }
}

/**
 * Get character count for a specific world
 */
export async function getWorldCharacterCount(userId: string, worldId: string): Promise<number> {
  try {
    const { count, error } = await supabaseAdmin
      .from('characters')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId)
      .eq('world_id', worldId);

    if (error) {
      console.error('Error getting world character count:', error);
      throw new Error(`Failed to get world character count: ${error.message}`);
    }

    return count || 0;
  } catch (error) {
    console.error('Error in getWorldCharacterCount:', error);
    throw error;
  }
}

/**
 * Check if user can perform an action based on their subscription limits
 */
export async function checkUsageLimit(options: LimitCheckOptions): Promise<UsageCheckResult> {
  const { userId, limitType, worldId, increment = false } = options;

  try {
    // Get user's subscription tier
    const { data: user, error: userError } = await supabaseAdmin
      .from('users')
      .select('subscription_tier')
      .eq('id', userId)
      .single();

    if (userError) {
      throw new Error(`Failed to get user subscription: ${userError.message}`);
    }

    const subscriptionTier = user.subscription_tier as SubscriptionTier;
    const limit = getLimit(subscriptionTier, limitType);

    // Initialize usage if needed
    await initializeUserUsage(userId);

    let current = 0;
    let resetDate: string | undefined;

    switch (limitType) {
      case 'worlds':
        const usage = await getUserUsage(userId);
        current = usage?.worlds_created || 0;
        break;

      case 'characters':
        if (!worldId) {
          throw new Error('worldId is required for character limit checks');
        }
        current = await getWorldCharacterCount(userId, worldId);
        break;

      case 'ai_messages':
        const aiUsage = await getUserUsage(userId);
        current = aiUsage?.ai_messages_today || 0;
        resetDate = aiUsage?.ai_messages_reset_date;

        // Check if we need to reset daily count
        const today = new Date().toISOString().split('T')[0];
        if (resetDate && resetDate < today) {
          // Reset will happen automatically in the database function
          current = 0;
        }
        break;

      default:
        throw new Error(`Invalid limit type: ${limitType}`);
    }

    // Check if unlimited
    const unlimited = isUnlimited(subscriptionTier, limitType as 'worlds' | 'characters');
    const allowed = unlimited || current < limit;
    const remaining = unlimited ? -1 : Math.max(0, limit - current);

    // If incrementing and allowed, increment the usage
    if (increment && allowed) {
      await incrementUsage(userId, limitType, worldId);
      current += 1;
    }

    return {
      allowed,
      current: increment && allowed ? current : current,
      limit,
      remaining: unlimited ? -1 : Math.max(0, limit - (increment && allowed ? current : current)),
      resetDate: limitType === 'ai_messages' ? resetDate : undefined,
      message: allowed ? undefined : `${limitType} limit exceeded (${current}/${limit})`
    };
  } catch (error) {
    console.error('Error in checkUsageLimit:', error);
    throw error;
  }
}

/**
 * Increment usage counter for a specific limit type
 */
export async function incrementUsage(
  userId: string,
  limitType: LimitType,
  worldId?: string
): Promise<number> {
  try {
    let newCount = 0;

    switch (limitType) {
      case 'worlds':
        const { data: worldData, error: worldError } = await supabaseAdmin.rpc(
          'increment_world_count',
          { p_user_id: userId }
        );
        if (worldError) throw worldError;
        newCount = worldData;
        break;

      case 'characters':
        if (!worldId) {
          throw new Error('worldId is required for character increment');
        }
        const { data: charData, error: charError } = await supabaseAdmin.rpc(
          'increment_character_count',
          { p_user_id: userId, p_world_id: worldId }
        );
        if (charError) throw charError;
        newCount = charData;
        break;

      case 'ai_messages':
        const { data: msgData, error: msgError } = await supabaseAdmin.rpc(
          'increment_ai_message_count',
          { p_user_id: userId }
        );
        if (msgError) throw msgError;
        newCount = msgData;
        break;

      default:
        throw new Error(`Invalid limit type: ${limitType}`);
    }

    return newCount;
  } catch (error) {
    console.error('Error in incrementUsage:', error);
    throw error;
  }
}

/**
 * Decrement usage counter (for deletions)
 */
export async function decrementUsage(
  userId: string,
  limitType: LimitType,
  worldId?: string
): Promise<number> {
  try {
    let newCount = 0;

    switch (limitType) {
      case 'worlds':
        if (!worldId) {
          throw new Error('worldId is required for world decrement');
        }
        const { data: worldData, error: worldError } = await supabaseAdmin.rpc(
          'decrement_world_count',
          { p_user_id: userId, p_world_id: worldId }
        );
        if (worldError) throw worldError;
        newCount = worldData;
        break;

      case 'characters':
        if (!worldId) {
          throw new Error('worldId is required for character decrement');
        }
        const { data: charData, error: charError } = await supabaseAdmin.rpc(
          'decrement_character_count',
          { p_user_id: userId, p_world_id: worldId }
        );
        if (charError) throw charError;
        newCount = charData;
        break;

      case 'ai_messages':
        // AI messages don't decrement (daily reset only)
        throw new Error('AI messages cannot be decremented');

      default:
        throw new Error(`Invalid limit type: ${limitType}`);
    }

    return newCount;
  } catch (error) {
    console.error('Error in decrementUsage:', error);
    throw error;
  }
}

/**
 * Get comprehensive usage statistics for a user
 */
export async function getUserUsageStats(userId: string): Promise<UserUsageStats> {
  try {
    // Get user's subscription tier
    const { data: user, error: userError } = await supabaseAdmin
      .from('users')
      .select('subscription_tier')
      .eq('id', userId)
      .single();

    if (userError) {
      throw new Error(`Failed to get user subscription: ${userError.message}`);
    }

    const subscriptionTier = user.subscription_tier as SubscriptionTier;

    // Get usage stats
    const worldsCheck = await checkUsageLimit({ userId, limitType: 'worlds' });
    const aiMessagesCheck = await checkUsageLimit({ userId, limitType: 'ai_messages' });

    return {
      worlds: worldsCheck,
      ai_messages_daily: aiMessagesCheck,
      subscription_tier: subscriptionTier
    };
  } catch (error) {
    console.error('Error in getUserUsageStats:', error);
    throw error;
  }
}

/**
 * Check limit and throw error if exceeded
 */
export async function enforceUsageLimit(options: LimitCheckOptions): Promise<void> {
  const result = await checkUsageLimit(options);

  if (!result.allowed) {
    throw new SubscriptionLimitError(
      options.limitType,
      result.current,
      result.limit,
      (await supabaseAdmin
        .from('users')
        .select('subscription_tier')
        .eq('id', options.userId)
        .single()).data?.subscription_tier as SubscriptionTier,
      result.message
    );
  }
}
