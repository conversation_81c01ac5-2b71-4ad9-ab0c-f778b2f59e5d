-- Create user_usage table for tracking subscription limits
CREATE TABLE IF NOT EXISTS public.user_usage (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    user_id uuid NOT NULL,
    worlds_created integer DEFAULT 0 NOT NULL,
    characters_created integer DEFAULT 0 NOT NULL,
    ai_messages_today integer DEFAULT 0 NOT NULL,
    ai_messages_reset_date date DEFAULT CURRENT_DATE NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    CONSTRAINT user_usage_pkey PRIMARY KEY (id),
    CONSTRAINT user_usage_user_id_key UNIQUE (user_id),
    CONSTRAINT user_usage_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE
);

-- Create index for efficient queries
CREATE INDEX IF NOT EXISTS idx_user_usage_user_id ON public.user_usage(user_id);
CREATE INDEX IF NOT EXISTS idx_user_usage_reset_date ON public.user_usage(ai_messages_reset_date);

-- Create world_character_counts table for tracking characters per world
CREATE TABLE IF NOT EXISTS public.world_character_counts (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    user_id uuid NOT NULL,
    world_id uuid NOT NULL,
    character_count integer DEFAULT 0 NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    CONSTRAINT world_character_counts_pkey PRIMARY KEY (id),
    CONSTRAINT world_character_counts_user_world_key UNIQUE (user_id, world_id),
    CONSTRAINT world_character_counts_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE,
    CONSTRAINT world_character_counts_world_id_fkey FOREIGN KEY (world_id) REFERENCES public.worlds(id) ON DELETE CASCADE
);

-- Create index for efficient queries
CREATE INDEX IF NOT EXISTS idx_world_character_counts_user_id ON public.world_character_counts(user_id);
CREATE INDEX IF NOT EXISTS idx_world_character_counts_world_id ON public.world_character_counts(world_id);

-- Function to reset daily AI message counts
CREATE OR REPLACE FUNCTION reset_daily_ai_messages()
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
    UPDATE public.user_usage 
    SET 
        ai_messages_today = 0,
        ai_messages_reset_date = CURRENT_DATE,
        updated_at = now()
    WHERE ai_messages_reset_date < CURRENT_DATE;
END;
$$;

-- Function to initialize user usage record
CREATE OR REPLACE FUNCTION initialize_user_usage(p_user_id uuid)
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
    INSERT INTO public.user_usage (user_id, worlds_created, characters_created, ai_messages_today, ai_messages_reset_date)
    VALUES (p_user_id, 0, 0, 0, CURRENT_DATE)
    ON CONFLICT (user_id) DO NOTHING;
END;
$$;

-- Function to increment world count
CREATE OR REPLACE FUNCTION increment_world_count(p_user_id uuid)
RETURNS integer
LANGUAGE plpgsql
AS $$
DECLARE
    new_count integer;
BEGIN
    -- Ensure user usage record exists
    PERFORM initialize_user_usage(p_user_id);
    
    -- Increment world count
    UPDATE public.user_usage 
    SET 
        worlds_created = worlds_created + 1,
        updated_at = now()
    WHERE user_id = p_user_id
    RETURNING worlds_created INTO new_count;
    
    RETURN new_count;
END;
$$;

-- Function to increment character count for a world
CREATE OR REPLACE FUNCTION increment_character_count(p_user_id uuid, p_world_id uuid)
RETURNS integer
LANGUAGE plpgsql
AS $$
DECLARE
    new_count integer;
BEGIN
    -- Ensure user usage record exists
    PERFORM initialize_user_usage(p_user_id);
    
    -- Increment total character count
    UPDATE public.user_usage 
    SET 
        characters_created = characters_created + 1,
        updated_at = now()
    WHERE user_id = p_user_id;
    
    -- Increment world-specific character count
    INSERT INTO public.world_character_counts (user_id, world_id, character_count)
    VALUES (p_user_id, p_world_id, 1)
    ON CONFLICT (user_id, world_id) 
    DO UPDATE SET 
        character_count = world_character_counts.character_count + 1,
        updated_at = now()
    RETURNING character_count INTO new_count;
    
    RETURN new_count;
END;
$$;

-- Function to increment AI message count
CREATE OR REPLACE FUNCTION increment_ai_message_count(p_user_id uuid)
RETURNS integer
LANGUAGE plpgsql
AS $$
DECLARE
    new_count integer;
BEGIN
    -- Ensure user usage record exists
    PERFORM initialize_user_usage(p_user_id);
    
    -- Reset count if it's a new day
    PERFORM reset_daily_ai_messages();
    
    -- Increment AI message count
    UPDATE public.user_usage 
    SET 
        ai_messages_today = ai_messages_today + 1,
        updated_at = now()
    WHERE user_id = p_user_id
    RETURNING ai_messages_today INTO new_count;
    
    RETURN new_count;
END;
$$;

-- Function to decrement character count when character is deleted
CREATE OR REPLACE FUNCTION decrement_character_count(p_user_id uuid, p_world_id uuid)
RETURNS integer
LANGUAGE plpgsql
AS $$
DECLARE
    new_count integer;
BEGIN
    -- Decrement total character count
    UPDATE public.user_usage 
    SET 
        characters_created = GREATEST(0, characters_created - 1),
        updated_at = now()
    WHERE user_id = p_user_id;
    
    -- Decrement world-specific character count
    UPDATE public.world_character_counts 
    SET 
        character_count = GREATEST(0, character_count - 1),
        updated_at = now()
    WHERE user_id = p_user_id AND world_id = p_world_id
    RETURNING character_count INTO new_count;
    
    -- Remove record if count reaches 0
    DELETE FROM public.world_character_counts 
    WHERE user_id = p_user_id AND world_id = p_world_id AND character_count = 0;
    
    RETURN COALESCE(new_count, 0);
END;
$$;

-- Function to decrement world count when world is deleted
CREATE OR REPLACE FUNCTION decrement_world_count(p_user_id uuid, p_world_id uuid)
RETURNS integer
LANGUAGE plpgsql
AS $$
DECLARE
    new_count integer;
BEGIN
    -- Decrement world count
    UPDATE public.user_usage 
    SET 
        worlds_created = GREATEST(0, worlds_created - 1),
        updated_at = now()
    WHERE user_id = p_user_id
    RETURNING worlds_created INTO new_count;
    
    -- Remove world character count record
    DELETE FROM public.world_character_counts 
    WHERE user_id = p_user_id AND world_id = p_world_id;
    
    RETURN new_count;
END;
$$;

-- Set up RLS (Row Level Security)
ALTER TABLE public.user_usage ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.world_character_counts ENABLE ROW LEVEL SECURITY;

-- RLS policies for user_usage
CREATE POLICY "Users can view their own usage" ON public.user_usage
    FOR SELECT USING (user_id = get_current_user_id());

CREATE POLICY "Users can update their own usage" ON public.user_usage
    FOR UPDATE USING (user_id = get_current_user_id());

CREATE POLICY "System can insert usage records" ON public.user_usage
    FOR INSERT WITH CHECK (true);

-- RLS policies for world_character_counts
CREATE POLICY "Users can view their own world character counts" ON public.world_character_counts
    FOR SELECT USING (user_id = get_current_user_id());

CREATE POLICY "Users can update their own world character counts" ON public.world_character_counts
    FOR UPDATE USING (user_id = get_current_user_id());

CREATE POLICY "System can insert world character count records" ON public.world_character_counts
    FOR INSERT WITH CHECK (true);

-- Grant permissions
ALTER TABLE public.user_usage OWNER TO postgres;
ALTER TABLE public.world_character_counts OWNER TO postgres;

-- Comments for documentation
COMMENT ON TABLE public.user_usage IS 'Tracks user subscription usage metrics';
COMMENT ON COLUMN public.user_usage.worlds_created IS 'Total number of worlds created by user';
COMMENT ON COLUMN public.user_usage.characters_created IS 'Total number of characters created by user';
COMMENT ON COLUMN public.user_usage.ai_messages_today IS 'Number of AI messages sent today';
COMMENT ON COLUMN public.user_usage.ai_messages_reset_date IS 'Date when AI message count was last reset';

COMMENT ON TABLE public.world_character_counts IS 'Tracks character count per world for subscription limits';
COMMENT ON COLUMN public.world_character_counts.character_count IS 'Number of characters in this specific world';
