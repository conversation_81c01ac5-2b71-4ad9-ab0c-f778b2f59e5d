-- Create user_usage table for tracking subscription limits
-- Simplified to only track messages since worlds/characters are now unlimited
CREATE TABLE IF NOT EXISTS public.user_usage (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    user_id uuid NOT NULL,
    messages_this_month integer DEFAULT 0 NOT NULL,
    messages_reset_date date DEFAULT CURRENT_DATE NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    CONSTRAINT user_usage_pkey PRIMARY KEY (id),
    CONSTRAINT user_usage_user_id_key UNIQUE (user_id),
    CONSTRAINT user_usage_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE
);

-- Create index for efficient queries
CREATE INDEX IF NOT EXISTS idx_user_usage_user_id ON public.user_usage(user_id);
CREATE INDEX IF NOT EXISTS idx_user_usage_reset_date ON public.user_usage(messages_reset_date);

-- Function to reset monthly message counts
CREATE OR REPLACE FUNCTION reset_monthly_messages()
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
    UPDATE public.user_usage
    SET
        messages_this_month = 0,
        messages_reset_date = CURRENT_DATE,
        updated_at = now()
    WHERE messages_reset_date < DATE_TRUNC('month', CURRENT_DATE);
END;
$$;

-- Function to initialize user usage record
CREATE OR REPLACE FUNCTION initialize_user_usage(p_user_id uuid)
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
    INSERT INTO public.user_usage (user_id, messages_this_month, messages_reset_date)
    VALUES (p_user_id, 0, CURRENT_DATE)
    ON CONFLICT (user_id) DO NOTHING;
END;
$$;

-- Function to increment message count
CREATE OR REPLACE FUNCTION increment_message_count(p_user_id uuid)
RETURNS integer
LANGUAGE plpgsql
AS $$
DECLARE
    new_count integer;
BEGIN
    -- Ensure user usage record exists
    PERFORM initialize_user_usage(p_user_id);

    -- Reset count if it's a new month
    PERFORM reset_monthly_messages();

    -- Increment message count
    UPDATE public.user_usage
    SET
        messages_this_month = messages_this_month + 1,
        updated_at = now()
    WHERE user_id = p_user_id
    RETURNING messages_this_month INTO new_count;

    RETURN new_count;
END;
$$;



-- Note: increment_ai_message_count is now replaced by increment_message_count above


-- Set up RLS (Row Level Security)
ALTER TABLE public.user_usage ENABLE ROW LEVEL SECURITY;

-- RLS policies for user_usage
CREATE POLICY "Users can view their own usage" ON public.user_usage
    FOR SELECT USING (user_id = get_current_user_id());

CREATE POLICY "Users can update their own usage" ON public.user_usage
    FOR UPDATE USING (user_id = get_current_user_id());

CREATE POLICY "System can insert usage records" ON public.user_usage
    FOR INSERT WITH CHECK (true);

-- Grant permissions
ALTER TABLE public.user_usage OWNER TO postgres;

-- Comments for documentation
COMMENT ON TABLE public.user_usage IS 'Tracks user subscription usage metrics';
COMMENT ON COLUMN public.user_usage.messages_this_month IS 'Number of messages sent this month';
COMMENT ON COLUMN public.user_usage.messages_reset_date IS 'Date when message count was last reset';
