-- Create user_usage table for tracking subscription limits
CREATE TABLE IF NOT EXISTS public.user_usage (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    user_id uuid NOT NULL,
    worlds_created integer DEFAULT 0 NOT NULL,
    characters_created integer DEFAULT 0 NOT NULL,
    ai_messages_today integer DEFAULT 0 NOT NULL,
    ai_messages_reset_date date DEFAULT CURRENT_DATE NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    CONSTRAINT user_usage_pkey PRIMARY KEY (id),
    CONSTRAINT user_usage_user_id_key UNIQUE (user_id),
    CONSTRAINT user_usage_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE
);

-- Create index for efficient queries
CREATE INDEX IF NOT EXISTS idx_user_usage_user_id ON public.user_usage(user_id);
CREATE INDEX IF NOT EXISTS idx_user_usage_reset_date ON public.user_usage(ai_messages_reset_date);

-- Function to reset daily AI message counts
CREATE OR REPLACE FUNCTION reset_daily_ai_messages()
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
    UPDATE public.user_usage
    SET
        ai_messages_today = 0,
        ai_messages_reset_date = date_trunc('week', CURRENT_DATE),
        updated_at = now()
    WHERE ai_messages_reset_date < date_trunc('week', CURRENT_DATE);
END;
$$;

-- Function to initialize user usage record
CREATE OR REPLACE FUNCTION initialize_user_usage(p_user_id uuid)
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
    INSERT INTO public.user_usage (user_id, worlds_created, characters_created, ai_messages_today, ai_messages_reset_date)
    VALUES (p_user_id, 0, 0, 0, date_trunc('week', CURRENT_DATE))
    ON CONFLICT (user_id) DO NOTHING;
END;
$$;

-- Function to increment world count
CREATE OR REPLACE FUNCTION increment_world_count(p_user_id uuid)
RETURNS integer
LANGUAGE plpgsql
AS $$
DECLARE
    new_count integer;
BEGIN
    -- Ensure user usage record exists
    PERFORM initialize_user_usage(p_user_id);

    -- Increment world count
    UPDATE public.user_usage
    SET
        worlds_created = worlds_created + 1,
        updated_at = now()
    WHERE user_id = p_user_id
    RETURNING worlds_created INTO new_count;

    RETURN new_count;
END;
$$;



-- Function to increment AI message count
CREATE OR REPLACE FUNCTION increment_ai_message_count(p_user_id uuid)
RETURNS integer
LANGUAGE plpgsql
AS $$
DECLARE
    new_count integer;
BEGIN
    -- Ensure user usage record exists
    PERFORM initialize_user_usage(p_user_id);

    -- Reset count if it's a new week
    PERFORM reset_daily_ai_messages();

    -- Increment AI message count
    UPDATE public.user_usage
    SET
        ai_messages_today = ai_messages_today + 1,
        updated_at = now()
    WHERE user_id = p_user_id
    RETURNING ai_messages_today INTO new_count;

    RETURN new_count;
END;
$$;


-- Set up RLS (Row Level Security)
ALTER TABLE public.user_usage ENABLE ROW LEVEL SECURITY;

-- RLS policies for user_usage
CREATE POLICY "Users can view their own usage" ON public.user_usage
    FOR SELECT USING (user_id = get_current_user_id());

CREATE POLICY "Users can update their own usage" ON public.user_usage
    FOR UPDATE USING (user_id = get_current_user_id());

CREATE POLICY "System can insert usage records" ON public.user_usage
    FOR INSERT WITH CHECK (true);

-- Grant permissions
ALTER TABLE public.user_usage OWNER TO postgres;

-- Comments for documentation
COMMENT ON TABLE public.user_usage IS 'Tracks user subscription usage metrics';
COMMENT ON COLUMN public.user_usage.worlds_created IS 'Total number of worlds created by user';
COMMENT ON COLUMN public.user_usage.characters_created IS 'Total number of characters created by user';
COMMENT ON COLUMN public.user_usage.ai_messages_today IS 'Number of AI messages sent today';
COMMENT ON COLUMN public.user_usage.ai_messages_reset_date IS 'Date when AI message count was last reset';
