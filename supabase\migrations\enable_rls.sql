-- Enable Row Level Security on tables
ALTER TABLE worlds ENABLE ROW LEVEL SECURITY;
ALTER TABLE characters ENABLE ROW LEVEL SECURITY;
ALTER TABLE campaigns ENABLE ROW LEVEL SECURITY;
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;

-- Worlds table policies
CREATE POLICY "Allow users to select their own worlds"
ON worlds FOR SELECT
USING (auth.uid()::text = user_id::text);

CREATE POLICY "Allow users to insert their own worlds"
ON worlds FOR INSERT
WITH CHECK (auth.uid()::text = user_id::text);

CREATE POLICY "Allow users to update their own worlds"
ON worlds FOR UPDATE
USING (auth.uid()::text = user_id::text);

CREATE POLICY "Allow users to delete their own worlds"
ON worlds FOR DELETE
USING (auth.uid()::text = user_id::text);

-- Characters table policies
CREATE POLICY "Allow users to select their own characters"
ON characters FOR SELECT
USING (auth.uid()::text = user_id::text);

CREATE POLICY "Allow users to insert their own characters"
ON characters FOR INSERT
WITH CHECK (auth.uid()::text = user_id::text);

CREATE POLICY "Allow users to update their own characters"
ON characters FOR UPDATE
USING (auth.uid()::text = user_id::text);

CREATE POLICY "Allow users to delete their own characters"
ON characters FOR DELETE
USING (auth.uid()::text = user_id::text);

-- Campaigns table policies - based on world ownership
CREATE POLICY "Allow users to select campaigns in their worlds"
ON campaigns FOR SELECT
USING (EXISTS (
  SELECT 1 FROM worlds 
  WHERE worlds.id = campaigns.world_id 
  AND worlds.user_id::text = auth.uid()::text
));

CREATE POLICY "Allow users to insert campaigns in their worlds"
ON campaigns FOR INSERT
WITH CHECK (EXISTS (
  SELECT 1 FROM worlds 
  WHERE worlds.id = campaigns.world_id 
  AND worlds.user_id::text = auth.uid()::text
));

CREATE POLICY "Allow users to update campaigns in their worlds"
ON campaigns FOR UPDATE
USING (EXISTS (
  SELECT 1 FROM worlds 
  WHERE worlds.id = campaigns.world_id 
  AND worlds.user_id::text = auth.uid()::text
));

CREATE POLICY "Allow users to delete campaigns in their worlds"
ON campaigns FOR DELETE
USING (EXISTS (
  SELECT 1 FROM worlds 
  WHERE worlds.id = campaigns.world_id 
  AND worlds.user_id::text = auth.uid()::text
));

-- Messages table policies
CREATE POLICY "Allow users to select their own messages"
ON messages FOR SELECT
USING (auth.uid()::text = user_id::text);

CREATE POLICY "Allow users to select messages in their worlds"
ON messages FOR SELECT
USING (EXISTS (
  SELECT 1 FROM worlds 
  WHERE worlds.id = messages.world_id 
  AND worlds.user_id::text = auth.uid()::text
));

CREATE POLICY "Allow users to insert their own messages"
ON messages FOR INSERT
WITH CHECK (auth.uid()::text = user_id::text);

CREATE POLICY "Allow users to update their own messages"
ON messages FOR UPDATE
USING (auth.uid()::text = user_id::text);

CREATE POLICY "Allow users to delete their own messages"
ON messages FOR DELETE
USING (auth.uid()::text = user_id::text);
