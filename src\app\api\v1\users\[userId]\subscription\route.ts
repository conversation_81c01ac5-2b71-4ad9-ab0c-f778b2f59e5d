import { NextRequest, NextResponse } from 'next/server';
import { requireUserSession, requireSelfOrAdmin } from '@/lib/auth-helpers';
import { updateUserSubscriptionTier } from '@/lib/user-service';
import { SubscriptionTier } from '@/types/schema.types';

/**
 * GET /api/v1/users/[userId]/subscription
 * Get user's subscription information
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const session = await requireUserSession(request);
    const userId = params.userId;
    const targetUserId = userId === 'me' ? session.user.id : userId;
    await requireSelfOrAdmin(session, targetUserId);

    // Return subscription tier from session (already fetched in NextAuth)
    return NextResponse.json({
      user_id: targetUserId,
      subscription_tier: session.user.subscription_tier || 'free',
      features: getSubscriptionFeatures(session.user.subscription_tier || 'free')
    });
  } catch (error) {
    console.error('Error getting user subscription:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/v1/users/[userId]/subscription
 * Update user's subscription tier (admin only for now)
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const session = await requireUserSession(request);
    const userId = params.userId;
    const targetUserId = userId === 'me' ? session.user.id : userId;
    
    // For now, only allow admins to change subscription tiers
    // In the future, this could be integrated with payment processing
    if (!session.user.is_admin) {
      return NextResponse.json(
        { error: 'Only administrators can modify subscription tiers' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { subscription_tier } = body;

    // Validate subscription tier
    const validTiers: SubscriptionTier[] = ['free', 'pro', 'pro_plus'];
    if (!validTiers.includes(subscription_tier)) {
      return NextResponse.json(
        { error: 'Invalid subscription tier. Must be one of: free, pro, pro_plus' },
        { status: 400 }
      );
    }

    // Update the subscription tier
    const updatedUser = await updateUserSubscriptionTier(targetUserId, subscription_tier);
    
    if (!updatedUser) {
      return NextResponse.json(
        { error: 'Failed to update subscription tier' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      user_id: targetUserId,
      subscription_tier: updatedUser.subscription_tier,
      features: getSubscriptionFeatures(updatedUser.subscription_tier),
      updated_at: updatedUser.updated_at
    });
  } catch (error) {
    console.error('Error updating user subscription:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

/**
 * Get features available for each subscription tier
 */
function getSubscriptionFeatures(tier: SubscriptionTier) {
  const features = {
    free: {
      max_worlds: 3,
      max_characters_per_world: 5,
      ai_messages_per_day: 50,
      custom_world_settings: false,
      priority_support: false,
      advanced_ai_models: false,
      export_data: false
    },
    pro: {
      max_worlds: 10,
      max_characters_per_world: 15,
      ai_messages_per_day: 200,
      custom_world_settings: true,
      priority_support: true,
      advanced_ai_models: false,
      export_data: true
    },
    pro_plus: {
      max_worlds: -1, // unlimited
      max_characters_per_world: -1, // unlimited
      ai_messages_per_day: 1000,
      custom_world_settings: true,
      priority_support: true,
      advanced_ai_models: true,
      export_data: true
    }
  };

  return features[tier] || features.free;
}
