-- Add a special policy for realtime subscriptions
-- This policy allows users to receive realtime updates for their own messages

-- First, check if the policy already exists and drop it if it does
DROP POLICY IF EXISTS "Allow realtime updates for user messages" ON messages;

-- Create the policy with a more permissive condition for realtime
CREATE POLICY "Allow realtime updates for user messages"
ON messages
FOR SELECT
USING (
  -- Match on user_id for the user's own messages
  (auth.uid()::text = user_id::text)
  OR
  -- Also allow access to messages in worlds owned by the user
  EXISTS (
    SELECT 1 FROM worlds 
    WHERE worlds.id = messages.world_id 
    AND worlds.user_id::text = auth.uid()::text
  )
);

-- Ensure RLS is enabled
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;
