import NextAuth from 'next-auth'
import GoogleProvider from 'next-auth/providers/google'
import { getUserByEmail } from '@/lib/user-service'
import { v4 as uuidv4 } from 'uuid'
import { supabaseAdmin } from '@/lib/supabase-admin'
import jwt from 'jsonwebtoken'

// Extend the User type to include id and subscription tier
interface ExtendedUser {
  id?: string;
  name?: string | null;
  email?: string | null;
  image?: string | null;
  is_admin?: boolean;
  subscription_tier?: string;
}

// Check for proxy headers and forwarded headers
const detectProxyUrl = (headers: any) => {
  // Try to detect if we're behind a proxy by checking headers
  const xForwardedHost = headers?.get('x-forwarded-host');
  const xForwardedProto = headers?.get('x-forwarded-proto') || 'https';
  const host = headers?.get('host');

  if (xForwardedHost) {
    return `${xForwardedProto}://${xForwardedHost}`;
  } else if (host && !host.includes('localhost')) {
    // Only use host if it's not localhost
    return `https://${host}`;
  }

  // Fall back to the environment variable
  return process.env.NEXTAUTH_URL;
};

// console.log("[NextAuth] Initializing with URL:", process.env.NEXTAUTH_URL);
// console.log("[NextAuth] Google Client ID set:", Boolean(process.env.GOOGLE_CLIENT_ID));
// console.log("[NextAuth] Google Client Secret set:", Boolean(process.env.GOOGLE_CLIENT_SECRET));
// console.log("[NextAuth] Supabase URL set:", Boolean(process.env.NEXT_PUBLIC_SUPABASE_URL));
// console.log("[NextAuth] Supabase Key set:", Boolean(process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY));
// console.log("[NextAuth] Supabase Service Role Key set:", Boolean(process.env.SUPABASE_SERVICE_ROLE_KEY));

export const authOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID || '',
      clientSecret: process.env.GOOGLE_CLIENT_SECRET || '',
    }),
  ],
  pages: {
    signIn: '/signup',
  },
  // Configure the trusted hosts to support ngrok and other proxies
  trustHost: true,
  callbacks: {
    async jwt({ token, account, user }: any) {
      // Initial sign in
      if (account && user) {
        console.log("[NextAuth] JWT callback - initial sign in", {
          userId: user.id,
          email: user.email,
          provider: account.provider
        });

        // Store or update user in Supabase
        if (user.email) {
          try {
            // Check if we already have this user in the database by email
            const { data: existingUser, error: lookupError } = await supabaseAdmin
              .from('users')
              .select('*')
              .eq('email', user.email)
              .single();

            if (lookupError && lookupError.code !== 'PGRST116') {
              console.error('[NextAuth] Error looking up user by email:', lookupError);
            }

            let dbUserId;

            if (existingUser) {
              // If the user already exists, use their UUID
              dbUserId = existingUser.id;

              // Update the user's information if needed
              const { error: updateError } = await supabaseAdmin
                .from('users')
                .update({
                  name: user.name,
                  avatar_url: user.image,
                  updated_at: new Date().toISOString(),
                })
                .eq('id', dbUserId);

              if (updateError) {
                console.error('[NextAuth] Error updating existing user:', updateError);
              }
            } else {
              // Generate a new UUID for this user
              dbUserId = uuidv4();

              // Insert the new user
              const userData = {
                id: dbUserId,
                email: user.email,
                name: user.name,
                avatar_url: user.image,
                is_admin: false,
                subscription_tier: 'free', // Default to free tier
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
              };

              try {
                // First check if the table exists and is accessible
                const { data: tableInfo, error: tableError } = await supabaseAdmin
                  .from('users')
                  .select('id')
                  .limit(1);

                if (tableError) {
                  console.error('[NextAuth] Error accessing users table:', tableError);
                  throw new Error(`Table access error: ${tableError.message}`);
                }

                // Proceed with insertion
                const { data: insertData, error: insertError } = await supabaseAdmin
                  .from('users')
                  .insert(userData)
                  .select();

                if (insertError) {
                  console.error('[NextAuth] Error inserting new user:', insertError);
                  console.error('[NextAuth] Insert payload:', userData);

                  // Check if it's a duplicate key violation
                  if (insertError.code === '23505') {
                    // Try to get the user with the email
                    const { data: existingUserData, error: retrieveError } = await supabaseAdmin
                      .from('users')
                      .select('*')
                      .eq('email', user.email)
                      .single();

                    if (retrieveError) {
                      console.error('[NextAuth] Error retrieving existing user:', retrieveError);
                    } else if (existingUserData) {
                      dbUserId = existingUserData.id;
                    }
                  } else {
                    // For other errors, try a direct approach
                    const { error: directInsertError } = await supabaseAdmin
                      .from('users')
                      .insert(userData);

                    if (directInsertError) {
                      console.error('[NextAuth] Direct insert also failed:', directInsertError);
                    }
                  }
                }
              } catch (insertErr: any) {
                console.error('[NextAuth] Error in user management:', insertErr);
              }
            }

            // Store the database UUID in the token
            token.dbUserId = dbUserId;
          } catch (error) {
            console.error('[NextAuth] Error in user management:', error);
          }
        }

        return {
          ...token,
          accessToken: account.access_token,
          id: user.id || `google-${user.email?.toLowerCase()}`,
          // Store the database UUID separately to use with Supabase
          dbUserId: token.dbUserId
        }
      }

      return token
    },
    async session({ session, token }: any) {
      // Make sure to pass the user ID to the session
      if (session?.user) {
        // Pass the database UUID to the session (used for Supabase queries)
        session.user.id = token.dbUserId || token.id;
        session.accessToken = token.accessToken;

        // Fetch user's subscription tier from database
        try {
          const { data: userData, error } = await supabaseAdmin
            .from('users')
            .select('subscription_tier')
            .eq('id', session.user.id)
            .single();

          if (!error && userData) {
            session.user.subscription_tier = userData.subscription_tier;
          }
        } catch (error) {
          console.error('[NextAuth] Error fetching user subscription tier:', error);
        }

        // Create a Supabase JWT for use with the client
        const supabaseJwtSecret = process.env.SUPABASE_JWT_SECRET;
        if (supabaseJwtSecret) {
          try {
            // Create the JWT payload for Supabase
            const payload = {
              aud: "authenticated",
              exp: Math.floor(Date.now() / 1000) + (60 * 60), // 1 hour expiry
              sub: session.user.id, // The user ID from our database
              email: token.email,
              role: "authenticated",
              // Add additional claims that might help with realtime
              user_id: session.user.id, // Duplicate the user ID in a field that might be more accessible
              iat: Math.floor(Date.now() / 1000), // Issued at time
              jti: Math.random().toString(36).substring(2) // Unique token ID
            };

            // Sign the JWT with the Supabase JWT secret
            session.supabaseAccessToken = jwt.sign(payload, supabaseJwtSecret);
          } catch (error) {
            console.error('[NextAuth] Error creating Supabase JWT:', error);
          }
        } else {
          console.warn('[NextAuth] SUPABASE_JWT_SECRET not set, RLS will not work');
        }
      }
      return session
    },
  },
  debug: process.env.NODE_ENV === 'development',
  logger: {
    error(code: string, metadata: any) {
      console.error('[NextAuth] Error:', code, metadata);
    },
    warn(code: string) {
      console.warn('[NextAuth] Warning:', code);
    },
    debug(code: string, metadata: any) {
      console.log('[NextAuth] Debug:', code, metadata);
    },
  },
}

const handler = NextAuth(authOptions)

// Export the handler directly - this is the correct approach for Next.js App Router
export { handler as GET, handler as POST }