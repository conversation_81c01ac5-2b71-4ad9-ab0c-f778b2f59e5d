'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter, useSearchParams } from 'next/navigation'
import Image from 'next/image'
import Link from 'next/link'
import CharacterInfoOverlay from '@/components/CharacterInfoOverlay'
import SubscriptionBadge, { SubscriptionComparison } from '@/components/SubscriptionBadge'
import { SubscriptionTier } from '@/types/schema.types'


// Types for player character data
interface PlayerCharacter {
  id: string
  type: 'player' | 'ai'
  name: string
  gender: string
  race: string
  class: string
  background: string
  alignment: string
  attributes: Record<string, number>
  inventory: string
  description: string
  memories: string
  avatar?: string
}
interface UserStats {
  worlds_count: number;
  characters_count: number;
  messages_count: number;
  user_id: string;
  campaigns_count: number;
  created_at: string;
}

export default function ProfilePage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const searchParams = useSearchParams()
  const tabParam = searchParams.get('tab')

  const [activeTab, setActiveTab] = useState<'profile' | 'characters' | 'settings' | 'subscription'>(
    tabParam === 'characters' ? 'characters' :
    tabParam === 'settings' ? 'settings' :
    tabParam === 'subscription' ? 'subscription' : 'profile'
  )

  const [characters, setCharacters] = useState<PlayerCharacter[]>([])
  const [userStats, setUserStats] = useState<UserStats>({
    worlds_count: 0,
    characters_count: 0,
    messages_count: 0,
    user_id: '',
    created_at: '',
    campaigns_count: 0
  })

  const [isLoading, setIsLoading] = useState(true)
  const [charactersLoading, setCharactersLoading] = useState(false)
  const [statsLoading, setStatsLoading] = useState(false)
  const [subscriptionLoading, setSubscriptionLoading] = useState(false)
  const [subscriptionInfo, setSubscriptionInfo] = useState<{
    subscription_tier: SubscriptionTier;
    features: any;
  } | null>(null)
  const [error, setError] = useState<string | null>(null)

  const [selectedCharacter, setSelectedCharacter] = useState<PlayerCharacter | null>(null)

  // Redirect to home if not authenticated
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/')
    } else if (status === 'authenticated') {
      setIsLoading(false)
      loadCharacters()
      loadUserStats()
      loadSubscriptionInfo()
    }
  }, [status, router])

  // Load characters from Supabase via API
  const loadCharacters = async () => {
    if (!session?.user?.id) return

    try {
      setCharactersLoading(true)
      setError(null)

      // Use API v1 endpoint instead of direct service call
      const response = await fetch(`/api/v1/users/me/characters`)

      if (!response.ok) {
        throw new Error('Failed to load characters')
      }

      const data = await response.json()
      setCharacters(data.characters || [])
    } catch (err) {
      console.error('Error loading characters:', err)
      setError('Failed to load characters. Please try again later.')
    } finally {
      setCharactersLoading(false)
    }
  }

  // Load user stats from Supabase via API
  const loadUserStats = async () => {
    if (!session?.user?.id) return

    try {
      setStatsLoading(true)

      // Use API v1 endpoint instead of direct service call
      const response = await fetch(`/api/v1/users/me/stats`)

      if (!response.ok) {
        throw new Error('Failed to load user stats')
      }

      const data = await response.json()
      setUserStats({
        worlds_count: data.worlds_count || 0,
        characters_count: data.characters_count || 0,
        messages_count: data.messages_count || 0,
        user_id: data.user_id || '',
        created_at: data.created_at || '',
        campaigns_count: data.campaigns_count || 0
      })
    } catch (err) {
      console.error('Error loading user stats:', err)
      // We don't set an error here as it's not critical
    } finally {
      setStatsLoading(false)
    }
  }

  // Load subscription info from API
  const loadSubscriptionInfo = async () => {
    if (!session?.user?.id) return

    try {
      setSubscriptionLoading(true)
      setError(null)

      const response = await fetch(`/api/v1/users/me/subscription`)

      if (!response.ok) {
        throw new Error('Failed to load subscription info')
      }

      const data = await response.json()
      setSubscriptionInfo({
        subscription_tier: data.subscription_tier,
        features: data.features
      })
    } catch (err) {
      console.error('Error loading subscription info:', err)
      setError('Failed to load subscription info. Please try again later.')
    } finally {
      setSubscriptionLoading(false)
    }
  }

  // Handle character deletion
  const handleDeleteCharacter = async (characterId: string, characterName: string) => {
    if (!confirm(`Are you sure you want to delete ${characterName}?`)) {
      return
    }

    try {
      setError(null)

      const response = await fetch(`/api/v1/characters/${characterId}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        throw new Error('Failed to delete character')
      }

      // Remove character from state
      setCharacters(characters.filter(char => char.id !== characterId))

      // Refresh stats
      loadUserStats()
    } catch (err) {
      console.error('Error deleting character:', err)
      setError('Failed to delete character. Please try again later.')
    }
  }

  if (isLoading) {
    return <ProfileSkeleton />
  }

  return (
    <main className="container mx-auto py-8 px-4">
      <div className="max-w-4xl mx-auto bg-background-darker border border-gray-800 rounded-lg overflow-hidden shadow-xl">
        {/* Profile Header */}
        <div className="bg-gradient-to-r from-background-darker to-primary/20 p-6 flex flex-col md:flex-row items-center gap-6">
          <div className="w-24 h-24 rounded-full bg-primary flex items-center justify-center text-white overflow-hidden border-2 border-primary shadow-lg">
            {session?.user?.image ? (
              <Image
                src={session.user.image}
                alt={session.user.name || 'User'}
                width={96}
                height={96}
                className="object-cover"
              />
            ) : (
              <span className="text-3xl font-bold">{session?.user?.name?.charAt(0) || 'U'}</span>
            )}
          </div>

          <div className="text-center md:text-left">
            <h1 className="text-2xl font-medieval text-primary">
              {session?.user?.name || 'Adventurer'}
            </h1>
            <p className="text-text-secondary mt-1">
              {session?.user?.email || 'No email provided'}
            </p>
            <div className="flex items-center justify-center md:justify-start gap-2 mt-2">
              <SubscriptionBadge
                tier={session?.user?.subscription_tier || 'free'}
                size="sm"
              />
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-800">
          <div className="flex">
            <button
              className={`py-4 px-6 font-medium text-sm focus:outline-none transition-colors ${
                activeTab === 'profile'
                  ? 'text-primary border-b-2 border-primary'
                  : 'text-text-secondary hover:text-text'
              }`}
              onClick={() => {
                setActiveTab('profile')
                router.push('/profile', { scroll: false })
              }}
            >
              Profile
            </button>
            <button
              className={`py-4 px-6 font-medium text-sm focus:outline-none transition-colors ${
                activeTab === 'characters'
                  ? 'text-primary border-b-2 border-primary'
                  : 'text-text-secondary hover:text-text'
              }`}
              onClick={() => {
                setActiveTab('characters')
                router.push('/profile?tab=characters', { scroll: false })
              }}
            >
              Characters
            </button>
            <button
              className={`py-4 px-6 font-medium text-sm focus:outline-none transition-colors ${
                activeTab === 'subscription'
                  ? 'text-primary border-b-2 border-primary'
                  : 'text-text-secondary hover:text-text'
              }`}
              onClick={() => {
                setActiveTab('subscription')
                router.push('/profile?tab=subscription', { scroll: false })
              }}
            >
              Subscription
            </button>
            <button
              className={`py-4 px-6 font-medium text-sm focus:outline-none transition-colors ${
                activeTab === 'settings'
                  ? 'text-primary border-b-2 border-primary'
                  : 'text-text-secondary hover:text-text'
              }`}
              onClick={() => {
                setActiveTab('settings')
                router.push('/profile?tab=settings', { scroll: false })
              }}
            >
              Settings
            </button>
          </div>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'profile' && (
            <div className="space-y-6">
              <h2 className="text-xl font-medieval text-primary">User Profile</h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <label className="block text-text-secondary text-sm mb-1">Name</label>
                    <div className="p-3 bg-background border border-gray-800 rounded-md">
                      {session?.user?.name || 'Not provided'}
                    </div>
                  </div>

                  <div>
                    <label className="block text-text-secondary text-sm mb-1">Email</label>
                    <div className="p-3 bg-background border border-gray-800 rounded-md">
                      {session?.user?.email || 'Not provided'}
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-text-secondary text-sm mb-1">Account Type</label>
                    <div className="p-3 bg-background border border-gray-800 rounded-md">
                      Google Account
                    </div>
                  </div>

                  <div>
                    <label className="block text-text-secondary text-sm mb-1">Member Since</label>
                    <div className="p-3 bg-background border border-gray-800 rounded-md">
                      {userStats.created_at ? new Date(userStats.created_at).toLocaleDateString() : 'Loading...'}
                    </div>
                  </div>
                </div>
              </div>

              <div className="pt-4 border-t border-gray-800">
                <h3 className="text-lg font-medieval text-primary mb-4">Game Statistics</h3>

                <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
                  <div className="bg-background p-4 rounded-lg border border-gray-800 text-center">
                    <div className="text-2xl font-medieval text-primary">
                      {statsLoading ? (
                        <span className="inline-block w-6 h-6 rounded-full border-2 border-primary border-t-transparent animate-spin"></span>
                      ) : (
                        userStats.worlds_count
                      )}
                    </div>
                    <div className="text-text-secondary text-sm mt-1">Worlds</div>
                  </div>

                  <div className="bg-background p-4 rounded-lg border border-gray-800 text-center">
                    <div className="text-2xl font-medieval text-primary">
                      {statsLoading ? (
                        <span className="inline-block w-6 h-6 rounded-full border-2 border-primary border-t-transparent animate-spin"></span>
                      ) : (
                        userStats.campaigns_count ?? 0
                      )}
                    </div>
                    <div className="text-text-secondary text-sm mt-1">Campaigns</div>
                  </div>

                  <div className="bg-background p-4 rounded-lg border border-gray-800 text-center">
                    <div className="text-2xl font-medieval text-primary">
                      {statsLoading ? (
                        <span className="inline-block w-6 h-6 rounded-full border-2 border-primary border-t-transparent animate-spin"></span>
                      ) : (
                        userStats.characters_count
                      )}
                    </div>
                    <div className="text-text-secondary text-sm mt-1">Characters</div>
                  </div>

                  <div className="bg-background p-4 rounded-lg border border-gray-800 text-center">
                    <div className="text-2xl font-medieval text-primary">
                      {statsLoading ? (
                        <span className="inline-block w-6 h-6 rounded-full border-2 border-primary border-t-transparent animate-spin"></span>
                      ) : (
                        userStats.messages_count
                      )}
                    </div>
                    <div className="text-text-secondary text-sm mt-1">Messages</div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'characters' && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h2 className="text-xl font-medieval text-primary">Your Characters</h2>
                <Link href="/create-character" className="fantasy-button text-sm">
                  Create Character
                </Link>
              </div>

              {error && (
                <div className="p-4 bg-red-900/20 border border-red-900 rounded-lg text-red-400 text-sm">
                  {error}
                </div>
              )}

              {charactersLoading ? (
                <div className="flex items-center justify-center py-12">
                  <div className="loading-dots">
                    <div></div>
                    <div></div>
                    <div></div>
                  </div>
                </div>
              ) : characters.length > 0 ? (
                <div className="space-y-4">
                  {characters.map(character => (
                    <div
                      key={character.id}
                      className="bg-background p-4 rounded-lg border border-gray-800 flex flex-col sm:flex-row sm:items-center justify-between gap-4"
                    >
                      <div className="flex items-center gap-4">
                        <div className="w-12 h-12 rounded-full bg-primary/20 flex items-center justify-center">
                          <span className="text-lg font-medieval text-primary">{character.name.charAt(0)}</span>
                        </div>
                        <div>
                          <h3 className="font-medium">{character.name} {character.type && `(${character.type})`}</h3>
                          <p className="text-text-secondary text-sm">
                            {character.race} {character.class}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2 sm:self-end">
                        <button
                          onClick={() => setSelectedCharacter(character)}
                          className="text-sm text-text-secondary hover:text-primary transition-colors"
                        >
                          View
                        </button>
                        <span className="text-gray-600">•</span>
                        <button
                          onClick={() => handleDeleteCharacter(character.id, character.name)}
                          className="text-sm text-text-secondary hover:text-accent transition-colors"
                        >
                          Delete
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12 border border-dashed border-gray-800 rounded-lg">
                  <h3 className="text-lg font-medieval text-primary mb-2">No Characters Yet</h3>
                  <p className="text-text-secondary mb-4">Create your first character to begin your adventure</p>
                  <Link href="/create-character" className="fantasy-button">Create Character</Link>
                </div>
              )}

              {selectedCharacter && (
                <CharacterInfoOverlay
                  character={selectedCharacter}
                  onClose={() => setSelectedCharacter(null)}
                />
              )}
            </div>
          )}

          {activeTab === 'subscription' && (
            <div className="space-y-6">
              <h2 className="text-xl font-medieval text-primary">Subscription Management</h2>

              {subscriptionLoading ? (
                <div className="flex items-center justify-center py-12">
                  <div className="loading-dots">
                    <div></div>
                    <div></div>
                    <div></div>
                  </div>
                </div>
              ) : subscriptionInfo ? (
                <div className="space-y-6">
                  <div className="p-4 bg-background border border-gray-800 rounded-lg">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="font-medium">Current Plan</h3>
                      <SubscriptionBadge tier={subscriptionInfo.subscription_tier} />
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-text-secondary">Max Worlds</span>
                        <div className="font-bold">
                          {subscriptionInfo.features.max_worlds === -1 ? 'Unlimited' : subscriptionInfo.features.max_worlds}
                        </div>
                      </div>
                      <div>
                        <span className="text-text-secondary">Characters/World</span>
                        <div className="font-bold">
                          {subscriptionInfo.features.max_characters_per_world === -1 ? 'Unlimited' : subscriptionInfo.features.max_characters_per_world}
                        </div>
                      </div>
                      <div>
                        <span className="text-text-secondary">AI Messages/Day</span>
                        <div className="font-bold">{subscriptionInfo.features.ai_messages_per_day}</div>
                      </div>
                      <div>
                        <span className="text-text-secondary">Advanced Features</span>
                        <div className="font-bold">
                          {subscriptionInfo.features.advanced_ai_models ? '✓' : '✗'}
                        </div>
                      </div>
                    </div>
                  </div>

                  <SubscriptionComparison currentTier={subscriptionInfo.subscription_tier} />
                </div>
              ) : (
                <div className="text-center py-12">
                  <p className="text-text-secondary">Failed to load subscription information</p>
                  <button
                    onClick={loadSubscriptionInfo}
                    className="fantasy-button mt-4"
                  >
                    Retry
                  </button>
                </div>
              )}
            </div>
          )}

          {activeTab === 'settings' && (
            <div className="space-y-6">
              <h2 className="text-xl font-medieval text-primary">Account Settings</h2>

              <div className="space-y-4">
                <div className="p-4 bg-background border border-gray-800 rounded-lg">
                  <h3 className="font-medium mb-2">Theme Preferences</h3>
                  <div className="flex gap-3">
                    <button className="w-8 h-8 rounded-full bg-gray-900 border-2 border-primary flex items-center justify-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </button>
                    <button className="w-8 h-8 rounded-full bg-gray-100 border-2 border-transparent hover:border-gray-300"></button>
                  </div>
                </div>

                {/* <div className="p-4 bg-background border border-gray-800 rounded-lg">
                  <h3 className="font-medium mb-2">Notification Settings</h3>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Email Notifications</span>
                      <div className="w-12 h-6 rounded-full bg-gray-700 flex items-center p-1 cursor-pointer">
                        <div className="w-4 h-4 rounded-full bg-primary transform translate-x-6"></div>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Campaign Updates</span>
                      <div className="w-12 h-6 rounded-full bg-gray-700 flex items-center p-1 cursor-pointer">
                        <div className="w-4 h-4 rounded-full bg-primary transform translate-x-6"></div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="p-4 bg-background border border-gray-800 rounded-lg">
                  <h3 className="font-medium mb-2">Privacy Settings</h3>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Show Online Status</span>
                      <div className="w-12 h-6 rounded-full bg-gray-700 flex items-center p-1 cursor-pointer">
                        <div className="w-4 h-4 rounded-full bg-primary transform translate-x-6"></div>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Share Game Activity</span>
                      <div className="w-12 h-6 rounded-full bg-gray-700 flex items-center p-1 cursor-pointer">
                        <div className="w-4 h-4 rounded-full bg-gray-200 transform translate-x-0"></div>
                      </div>
                    </div>
                  </div>
                </div> */}
              </div>

              {/* <div className="flex justify-end space-x-4 pt-4">
                <button className="text-text-secondary hover:text-white transition-colors">Cancel</button>
                <button className="fantasy-button text-sm">Save Changes</button>
              </div> */}
            </div>
          )}
        </div>
      </div>

      <div className="mt-8 text-center">
        <Link href="/" className="text-text-secondary hover:text-primary transition-colors text-sm">
          ← Back to Home
        </Link>
      </div>
    </main>
  )
}

// Loading skeleton component
function ProfileSkeleton() {
  return (
    <main className="container mx-auto py-8 px-4">
      <div className="max-w-4xl mx-auto bg-background-darker border border-gray-800 rounded-lg overflow-hidden shadow-xl">
        {/* Profile Header Skeleton */}
        <div className="bg-gradient-to-r from-background-darker to-primary/20 p-6 flex flex-col md:flex-row items-center gap-6">
          <div className="w-24 h-24 rounded-full bg-gray-800 animate-pulse"></div>

          <div className="text-center md:text-left w-full md:w-auto">
            <div className="h-8 w-48 bg-gray-800 rounded animate-pulse mb-2"></div>
            <div className="h-4 w-32 bg-gray-800 rounded animate-pulse mb-2"></div>
            <div className="flex items-center justify-center md:justify-start gap-2 mt-2">
              <div className="h-6 w-20 bg-gray-800 rounded-full animate-pulse"></div>
              <div className="h-6 w-16 bg-gray-800 rounded-full animate-pulse"></div>
            </div>
          </div>
        </div>

        {/* Tabs Skeleton */}
        <div className="border-b border-gray-800">
          <div className="flex">
            <div className="py-4 px-6">
              <div className="h-4 w-16 bg-gray-800 rounded animate-pulse"></div>
            </div>
            <div className="py-4 px-6">
              <div className="h-4 w-20 bg-gray-800 rounded animate-pulse"></div>
            </div>
            <div className="py-4 px-6">
              <div className="h-4 w-18 bg-gray-800 rounded animate-pulse"></div>
            </div>
          </div>
        </div>

        {/* Content Skeleton */}
        <div className="p-6 space-y-6">
          <div className="h-6 w-32 bg-gray-800 rounded animate-pulse mb-6"></div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <div className="h-4 w-12 bg-gray-800 rounded animate-pulse mb-2"></div>
                <div className="h-10 bg-gray-800 rounded animate-pulse"></div>
              </div>

              <div>
                <div className="h-4 w-12 bg-gray-800 rounded animate-pulse mb-2"></div>
                <div className="h-10 bg-gray-800 rounded animate-pulse"></div>
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <div className="h-4 w-24 bg-gray-800 rounded animate-pulse mb-2"></div>
                <div className="h-10 bg-gray-800 rounded animate-pulse"></div>
              </div>

              <div>
                <div className="h-4 w-24 bg-gray-800 rounded animate-pulse mb-2"></div>
                <div className="h-10 bg-gray-800 rounded animate-pulse"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  )
}